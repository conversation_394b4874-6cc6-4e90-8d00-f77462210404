# Ultimate Trivia Challenge

A comprehensive quiz application with CRUD operations, API integration, charts, and local storage.

## Features
- **CRUD**: Create/Read/Update/Delete custom questions and quiz history
- **API**: OpenTDB integration with caching and offline fallback
- **Charts**: Performance trends (line) and category breakdown (doughnut)
- **Storage**: Local storage for all user data
- **Design**: Bootstrap 5, responsive, simplified interface

## Files
```
├── mee.html          # Main app
├── styles.css        # Minimal CSS
└── js/
    ├── app.js        # Core logic
    ├── api.js        # API & caching
    ├── storage.js    # Local storage CRUD
    └── dashboard.js  # Charts & stats
```

## Usage
1. **Quiz**: Select options → Answer questions → View results
2. **Dashboard**: Performance charts and quiz history
3. **Manage**: Add/edit custom questions

## Quick Start
Open `mee.html` in browser or run `python -m http.server 8000`
