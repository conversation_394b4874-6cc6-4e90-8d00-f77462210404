<!DOCTYPE html>
<html>
<head>
    <title>Local Storage Test</title>
</head>
<body>
    <h1>Local Storage Test</h1>
    <button onclick="testStorage()">Test Storage</button>
    <div id="result"></div>

    <script>
        function testStorage() {
            const resultDiv = document.getElementById('result');
            
            try {
                // Test if localStorage is available
                if (typeof(Storage) !== "undefined") {
                    // Test write
                    localStorage.setItem('test', 'Hello World');
                    
                    // Test read
                    const value = localStorage.getItem('test');
                    
                    if (value === 'Hello World') {
                        resultDiv.innerHTML = '<p style="color: green;">✅ Local Storage is working!</p>';
                    } else {
                        resultDiv.innerHTML = '<p style="color: red;">❌ Local Storage read failed</p>';
                    }
                    
                    // Clean up
                    localStorage.removeItem('test');
                } else {
                    resultDiv.innerHTML = '<p style="color: red;">❌ Local Storage not supported</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<p style="color: red;">❌ Error: ' + error.message + '</p>';
            }
        }
        
        // Auto test on load
        window.onload = testStorage;
    </script>
</body>
</html>
