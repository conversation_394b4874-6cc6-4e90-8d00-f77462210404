<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<title>🧠 Ultimate Trivia Challenge</title>
<meta name="viewport" content="width=device-width, initial-scale=1" />
<link
  href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
  rel="stylesheet"
/>
<link
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
  rel="stylesheet"
/>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
:root{--primary-gradient:linear-gradient(135deg,#667eea 0%,#764ba2 100%);--card-shadow:0 8px 25px rgba(0,0,0,0.1);--transition:all 0.3s ease}
body{font-family:Arial,sans-serif;background:var(--primary-gradient);min-height:100vh}
.container{background:rgba(255,255,255,0.98);border-radius:15px;box-shadow:var(--card-shadow);margin-top:10px;padding:20px}
.section{display:none}.section.active{display:block}
.navbar{background:rgba(255,255,255,0.95)!important;backdrop-filter:blur(20px)}
.navbar-brand{font-weight:700;color:#333!important}.navbar-brand i{color:#667eea;margin-right:8px}
.nav-link{color:#333!important;font-weight:500}.nav-link:hover{color:#667eea!important}
.card{border:none;box-shadow:var(--card-shadow);border-radius:15px;transition:var(--transition)}
.card:hover{transform:translateY(-2px)}
.btn{border-radius:25px;font-weight:500;transition:var(--transition)}
.btn-primary{background:var(--primary-gradient);border:none;color:white}
.btn-primary:hover{transform:translateY(-2px)}
.btn-outline-primary{border:2px solid #667eea;color:#667eea}
.btn-outline-primary:hover{background:var(--primary-gradient);color:white}
.btn-outline-primary.correct{background:#28a745;border-color:#28a745;color:white}
.btn-outline-primary.incorrect{background:#dc3545;border-color:#dc3545;color:white}
.form-control,.form-select{border-radius:10px;transition:var(--transition)}
.form-control:focus,.form-select:focus{border-color:#667eea;box-shadow:0 0 0 0.2rem rgba(102,126,234,0.25)}
.progress{height:10px;border-radius:10px}.progress-bar{background:var(--primary-gradient);border-radius:10px}
.badge{font-size:0.9rem;padding:8px 12px;border-radius:20px}
.badge.bg-secondary,.badge.bg-primary{background-color:#90ee90!important;color:#333!important}
.quiz-container,.manage-container{max-width:66.67%;margin:0 auto}
.table th{background:var(--primary-gradient);color:white;border:none}
.table tbody tr:hover{background:rgba(102,126,234,0.05)}
@media (max-width:768px){.quiz-container,.manage-container{max-width:100%}}
</style>
</head>
<body>
<nav class="navbar navbar-expand-lg">
<div class="container">
<a class="navbar-brand" href="#"><i class="fas fa-brain"></i> Trivia Challenge</a>
<div class="navbar-nav ms-auto">
<a class="nav-link" href="#" onclick="showSection('quiz-section')">Quiz</a>
<a class="nav-link" href="#" onclick="showSection('dashboard-section')">Dashboard</a>
<a class="nav-link" href="#" onclick="showSection('manage-section')">Manage</a>
</div>
</div>
</nav>

<div class="container mt-2">
<div id="quiz-section" class="section active">
<div class="quiz-container">
<div class="row mb-2">
<div class="col-4">
<div class="card text-center">
<div class="card-body p-2">
<small><i class="fas fa-trophy"></i> High Score</small>
<h6 id="high-score-display" class="mb-0">0</h6>
</div></div></div>
<div class="col-4">
<div class="card text-center">
<div class="card-body p-2">
<small><i class="fas fa-fire"></i> Streak</small>
<h6 id="streak-display" class="mb-0">0</h6>
</div></div></div>
<div class="col-4">
<div class="card text-center">
<div class="card-body p-2">
<small><i class="fas fa-star"></i> Points</small>
<h6 id="total-points-display" class="mb-0">0</h6>
</div></div></div>
</div>

<div id="settings" class="card">
<div class="card-body p-3">
<h6><i class="fas fa-cog"></i> Quiz Settings</h6>
<div class="row g-2">
<div class="col-md-3">
<select id="category" class="form-select form-select-sm">
<option value="">Any Category</option>
<option value="9">General Knowledge</option>
<option value="21">Sports</option>
<option value="23">History</option>
<option value="17">Science & Nature</option>
<option value="22">Geography</option>
</select>
</div>
<div class="col-md-3">
<select id="difficulty" class="form-select form-select-sm">
<option value="">Any Difficulty</option>
<option value="easy">Easy</option>
<option value="medium">Medium</option>
<option value="hard">Hard</option>
</select>
</div>
<div class="col-md-3">
<input id="amount" type="number" min="1" max="20" class="form-control form-control-sm" value="5" placeholder="Questions"/>
</div>
<div class="col-md-3">
<button class="btn btn-primary btn-sm w-100" onclick="startQuiz()">Start Quiz</button>
</div>
</div>
</div>
</div>

<div id="quiz" class="card mt-2" style="display:none">
<div class="card-body p-3">
<div class="progress mb-2">
<div class="progress-bar" id="progress-bar" style="width:0%"></div>
</div>
<div class="d-flex justify-content-between align-items-center mb-2">
<span class="badge bg-primary">Q <span id="current-q">1</span>/<span id="total-q">5</span></span>
<span class="badge bg-secondary">⏱️ <span id="timer-text">30</span>s</span>
</div>
<div class="card mb-2">
<div class="card-body p-3">
<h6 id="question-text" class="mb-0"></h6>
</div>
</div>
<div id="answers-grid" class="d-grid gap-2 mb-2"></div>
<div class="text-center">
<button class="btn btn-warning btn-sm" onclick="skipQuestion()">Skip</button>
</div>
</div>
</div>

      <div id="result" class="card mt-3" style="display: none">
        <div class="card-body text-center p-3">
          <h5 id="result-title">Quiz Completed!</h5>
          <p id="result-score" class="mb-2"></p>
          <div class="row mb-2">
            <div class="col-4">
              <div class="card">
                <div class="card-body p-2">
                  <small>Accuracy</small>
                  <h6 id="accuracy-display" class="mb-0">0%</h6>
                </div>
              </div>
            </div>
            <div class="col-4">
              <div class="card">
                <div class="card-body p-2">
                  <small>Avg Time</small>
                  <h6 id="avg-time-display" class="mb-0">0s</h6>
                </div>
              </div>
            </div>
            <div class="col-4">
              <div class="card">
                <div class="card-body p-2">
                  <small>Points</small>
                  <h6 id="points-earned" class="mb-0">0</h6>
                </div>
              </div>
            </div>
          </div>
          <div class="d-flex gap-2 justify-content-center">
            <button class="btn btn-primary btn-sm" onclick="restartQuiz()">
              Try Again
            </button>
            <button class="btn btn-success btn-sm" onclick="newQuiz()">
              New Quiz
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div id="dashboard-section" class="section">
    <h2>Dashboard</h2>
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-body">
            <h5>Performance Chart</h5>
            <canvas id="performanceChart"></canvas>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-body">
            <h5>Category Performance</h5>
            <canvas id="categoryChart"></canvas>
          </div>
        </div>
      </div>
    </div>
    <div class="card">
      <div class="card-body">
        <h5>Quiz History</h5>
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Category</th>
                <th>Score</th>
                <th>Accuracy</th>
                <th>Points</th>
              </tr>
            </thead>
            <tbody id="historyTableBody"></tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <div id="manage-section" class="section">
    <div class="manage-container">
      <h2>Manage Questions</h2>
      <div class="card mb-4">
        <div class="card-body">
          <h5>Add Custom Question</h5>
          <form id="questionForm">
            <div class="mb-3">
              <input
                type="text"
                class="form-control"
                id="questionInput"
                placeholder="Question"
                required
              />
            </div>
            <div class="mb-3">
              <input
                type="text"
                class="form-control"
                id="correctAnswer"
                placeholder="Correct Answer"
                required
              />
            </div>
            <div class="mb-3">
              <input
                type="text"
                class="form-control"
                id="wrongAnswer1"
                placeholder="Wrong Answer 1"
                required
              />
            </div>
            <div class="mb-3">
              <input
                type="text"
                class="form-control"
                id="wrongAnswer2"
                placeholder="Wrong Answer 2"
                required
              />
            </div>
            <div class="mb-3">
              <input
                type="text"
                class="form-control"
                id="wrongAnswer3"
                placeholder="Wrong Answer 3"
                required
              />
            </div>
            <div class="mb-3">
              <select class="form-select" id="questionCategory">
                <option value="custom">Custom</option>
                <option value="general">General Knowledge</option>
                <option value="science">Science</option>
                <option value="history">History</option>
              </select>
            </div>
            <button type="submit" class="btn btn-primary">Add Question</button>
          </form>
        </div>
      </div>
      <div class="card">
        <div class="card-body">
          <h5>Custom Questions</h5>
          <div class="table-responsive">
            <table class="table">
              <thead>
                <tr>
                  <th>Question</th>
                  <th>Category</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="questionsTableBody"></tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
  class StorageManager {
    constructor() {
      this.keys = {
        QUIZ_HISTORY: "trivia_quiz_history",
        CUSTOM_QUESTIONS: "trivia_custom_questions",
        STATS: "trivia_stats",
      };
    }
    get(key) {
      try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : null;
      } catch {
        return null;
      }
    }
    set(key, value) {
      try {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
      } catch {
        return false;
      }
    }
    getQuizHistory() {
      return this.get(this.keys.QUIZ_HISTORY) || [];
    }
    addQuizResult(result) {
      const history = this.getQuizHistory();
      const newResult = { ...result, id: Date.now(), date: new Date().toISOString() };
      history.unshift(newResult);
      if (history.length > 100) history.splice(100);
      this.set(this.keys.QUIZ_HISTORY, history);
      return newResult;
    }
    getCustomQuestions() {
      return this.get(this.keys.CUSTOM_QUESTIONS) || [];
    }
    addCustomQuestion(question) {
      const questions = this.getCustomQuestions();
      const newQuestion = { ...question, id: Date.now(), created: new Date().toISOString() };
      questions.push(newQuestion);
      this.set(this.keys.CUSTOM_QUESTIONS, questions);
      return newQuestion;
    }
    deleteCustomQuestion(id) {
      const questions = this.getCustomQuestions();
      const filtered = questions.filter((q) => q.id !== id);
      this.set(this.keys.CUSTOM_QUESTIONS, filtered);
      return filtered;
    }
    getStats() {
      return this.get(this.keys.STATS) || {
        totalQuizzes: 0,
        highScore: 0,
        totalPoints: 0,
        maxStreak: 0,
      };
    }
    saveStats(score, points, streak) {
      const stats = this.getStats();
      stats.totalQuizzes += 1;
      stats.totalPoints += points;
      if (score > stats.highScore) stats.highScore = score;
      if (streak > stats.maxStreak) stats.maxStreak = streak;
      this.set(this.keys.STATS, stats);
      return stats;
    }
  }

  class APIManager {
    constructor() {
      this.baseURL = "https://opentdb.com/api.php";
    }
    async fetchQuestions(params) {
      try {
        const url = this.buildURL(params);
        const response = await fetch(url);
        if (!response.ok) throw new Error("Network error");
        const data = await response.json();
        if (data.response_code === 0) return data.results;
        throw new Error("API error");
      } catch {
        const customQuestions = storage.getCustomQuestions();
        if (customQuestions.length > 0)
          return this.formatCustomQuestions(customQuestions, params.amount || 5);
        return this.getDefaultQuestions(params.amount || 5);
      }
    }
    buildURL(params) {
      let url = `${this.baseURL}?amount=${params.amount || 5}&type=multiple`;
      if (params.category) url += `&category=${params.category}`;
      if (params.difficulty) url += `&difficulty=${params.difficulty}`;
      return url;
    }
    formatCustomQuestions(questions, amount) {
      return questions
        .sort(() => 0.5 - Math.random())
        .slice(0, amount)
        .map((q) => ({
          question: q.question,
          correct_answer: q.correctAnswer,
          incorrect_answers: [q.wrongAnswer1, q.wrongAnswer2, q.wrongAnswer3],
          category: q.category,
          difficulty: "medium",
        }));
    }
    getDefaultQuestions(amount) {
      const defaultQuestions = [
        {
          question: "What is the capital of France?",
          correct_answer: "Paris",
          incorrect_answers: ["Berlin", "Rome", "Madrid"],
          category: "General Knowledge",
          difficulty: "easy",
        },
        {
          question: "Who painted the Mona Lisa?",
          correct_answer: "Leonardo da Vinci",
          incorrect_answers: ["Vincent Van Gogh", "Pablo Picasso", "Claude Monet"],
          category: "General Knowledge",
          difficulty: "easy",
        },
      ];
      return defaultQuestions.slice(0, amount);
    }
  }

  const storage = new StorageManager();
  const api = new APIManager();

  let quizData = [];
  let currentQuestionIndex = 0;
  let score = 0;
  let streak = 0;
  let timer = null;
  let timerDuration = 30;
  let timeLeft = timerDuration;
  let totalTimeTaken = 0;
  let quizStartedAt = null;

  function showSection(id) {
    document.querySelectorAll(".section").forEach((sec) => {
      sec.classList.remove("active");
    });
    document.getElementById(id).classList.add("active");
    if (id === "dashboard-section") loadDashboard();
    if (id === "manage-section") loadCustomQuestions();
  }

  async function startQuiz() {
    resetQuizState();
    const amount = parseInt(document.getElementById("amount").value) || 5;
    const category = document.getElementById("category").value;
    const difficulty = document.getElementById("difficulty").value;

    quizData = await api.fetchQuestions({ amount, category, difficulty });
    if (!quizData || quizData.length === 0) {
      alert("No questions found for the selected options.");
      return;
    }

    document.getElementById("settings").style.display = "none";
    document.getElementById("quiz").style.display = "block";
    document.getElementById("result").style.display = "none";

    currentQuestionIndex = 0;
    renderQuestion();
    quizStartedAt = Date.now();
    startTimer();
  }

  function resetQuizState() {
    score = 0;
    streak = 0;
    currentQuestionIndex = 0;
    timeLeft = timerDuration;
    totalTimeTaken = 0;
    clearInterval(timer);
    updateScoreDisplays();
  }

  function renderQuestion() {
    if (currentQuestionIndex >= quizData.length) {
      endQuiz();
      return;
    }

    const q = quizData[currentQuestionIndex];
    const questionText = decodeHtml(q.question);
    const answers = shuffleArray([
      q.correct_answer,
      ...q.incorrect_answers,
    ]).map(decodeHtml);

    document.getElementById("question-text").innerText = questionText;
    document.getElementById("current-q").innerText = currentQuestionIndex + 1;
    document.getElementById("total-q").innerText = quizData.length;
    document.getElementById("timer-text").innerText = timerDuration;

    const answersGrid = document.getElementById("answers-grid");
    answersGrid.innerHTML = "";
    answers.forEach((answer) => {
      const btn = document.createElement("button");
      btn.className = "btn btn-outline-primary";
      btn.innerText = answer;
      btn.onclick = () => handleAnswer(answer);
      answersGrid.appendChild(btn);
    });

    updateProgressBar();
    timeLeft = timerDuration;
  }

  function handleAnswer(selected) {
    clearInterval(timer);
    const currentQ = quizData[currentQuestionIndex];
    const correct = decodeHtml(currentQ.correct_answer);

    const buttons = document.querySelectorAll("#answers-grid button");
    buttons.forEach((btn) => {
      btn.disabled = true;
      if (btn.innerText === correct) {
        btn.classList.add("correct");
      }
      if (btn.innerText === selected && selected !== correct) {
        btn.classList.add("incorrect");
      }
    });

    if (selected === correct) {
      score++;
      streak++;
    } else {
      streak = 0;
    }

    totalTimeTaken += timerDuration - timeLeft;
    updateScoreDisplays();

    setTimeout(() => {
      currentQuestionIndex++;
      renderQuestion();
      startTimer();
    }, 1200);
  }

  function skipQuestion() {
    clearInterval(timer);
    streak = 0;
    currentQuestionIndex++;
    totalTimeTaken += timerDuration; // max time penalty for skip
    updateScoreDisplays();
    if (currentQuestionIndex >= quizData.length) {
      endQuiz();
      return;
    }
    renderQuestion();
    startTimer();
  }

  function startTimer() {
    timeLeft = timerDuration;
    document.getElementById("timer-text").innerText = timeLeft;
    timer = setInterval(() => {
      timeLeft--;
      document.getElementById("timer-text").innerText = timeLeft;
      if (timeLeft <= 0) {
        clearInterval(timer);
        skipQuestion();
      }
    }, 1000);
  }

  function updateScoreDisplays() {
    const stats = storage.getStats();
    document.getElementById("high-score-display").innerText = stats.highScore;
    document.getElementById("streak-display").innerText = streak;
    document.getElementById("total-points-display").innerText = stats.totalPoints + score * 10;
  }

  function updateProgressBar() {
    const progress = ((currentQuestionIndex + 1) / quizData.length) * 100;
    document.getElementById("progress-bar").style.width = `${progress}%`;
  }

  function endQuiz() {
    clearInterval(timer);
    document.getElementById("quiz").style.display = "none";
    document.getElementById("settings").style.display = "block";
    document.getElementById("result").style.display = "block";

    const accuracy = ((score / quizData.length) * 100).toFixed(1);
    const avgTime = (totalTimeTaken / quizData.length).toFixed(1);
    const pointsEarned = score * 10;

    document.getElementById("result-score").innerText = `You scored ${score} out of ${quizData.length} questions.`;
    document.getElementById("accuracy-display").innerText = `${accuracy}%`;
    document.getElementById("avg-time-display").innerText = `${avgTime}s`;
    document.getElementById("points-earned").innerText = pointsEarned;

    storage.addQuizResult({
      score,
      totalQuestions: quizData.length,
      accuracy,
      avgTime,
      points: pointsEarned,
      streak,
      category: document.getElementById("category").value || "Mixed",
      difficulty: document.getElementById("difficulty").value || "Mixed",
    });

    storage.saveStats(score, pointsEarned, streak);

    updateScoreDisplays();
  }

  function restartQuiz() {
    document.getElementById("result").style.display = "none";
    startQuiz();
  }

  function newQuiz() {
    document.getElementById("result").style.display = "none";
    document.getElementById("quiz").style.display = "none";
    document.getElementById("settings").style.display = "block";
  }

  function loadDashboard() {
    const history = storage.getQuizHistory();
    const ctxPerf = document.getElementById("performanceChart").getContext("2d");
    const ctxCat = document.getElementById("categoryChart").getContext("2d");

    const dates = history.map((h) => new Date(h.date).toLocaleDateString()).reverse();
    const scores = history.map((h) => h.score).reverse();
    const categories = {};
    history.forEach((h) => {
      categories[h.category] = (categories[h.category] || 0) + 1;
    });

    if (window.performanceChartInstance) window.performanceChartInstance.destroy();
    if (window.categoryChartInstance) window.categoryChartInstance.destroy();

    window.performanceChartInstance = new Chart(ctxPerf, {
      type: "line",
      data: {
        labels: dates,
        datasets: [
          {
            label: "Scores",
            data: scores,
            fill: false,
            borderColor: "#667eea",
            tension: 0.2,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: true,
        aspectRatio: 2,
        scales: {
          y: { beginAtZero: true, max: 10 },
        },
      },
    });

    window.categoryChartInstance = new Chart(ctxCat, {
      type: "doughnut",
      data: {
        labels: Object.keys(categories),
        datasets: [
          {
            data: Object.values(categories),
            backgroundColor: [
              "#667eea",
              "#764ba2",
              "#90ee90",
              "#fbc531",
              "#e67e22",
              "#e74c3c",
            ],
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: true,
        aspectRatio: 2,
      },
    });

    // Fill quiz history table
    const tbody = document.getElementById("historyTableBody");
    tbody.innerHTML = "";
    history.forEach((item) => {
      const tr = document.createElement("tr");
      tr.innerHTML = `
        <td>${new Date(item.date).toLocaleString()}</td>
        <td>${item.category}</td>
        <td>${item.score}/${item.totalQuestions}</td>
        <td>${item.accuracy}%</td>
        <td>${item.points}</td>
      `;
      tbody.appendChild(tr);
    });
  }

  function loadCustomQuestions() {
    const questions = storage.getCustomQuestions();
    const tbody = document.getElementById("questionsTableBody");
    tbody.innerHTML = "";
    questions.forEach((q) => {
      const tr = document.createElement("tr");
      tr.innerHTML = `
        <td>${q.question}</td>
        <td>${q.category}</td>
        <td>
          <button class="btn btn-danger btn-sm" onclick="deleteQuestion(${q.id})">
            Delete
          </button>
        </td>
      `;
      tbody.appendChild(tr);
    });
  }

  function deleteQuestion(id) {
    if (!confirm("Are you sure you want to delete this question?")) return;
    storage.deleteCustomQuestion(id);
    loadCustomQuestions();
  }

  document.getElementById("questionForm").addEventListener("submit", (e) => {
    e.preventDefault();
    const question = document.getElementById("questionInput").value.trim();
    const correctAnswer = document.getElementById("correctAnswer").value.trim();
    const wrongAnswer1 = document.getElementById("wrongAnswer1").value.trim();
    const wrongAnswer2 = document.getElementById("wrongAnswer2").value.trim();
    const wrongAnswer3 = document.getElementById("wrongAnswer3").value.trim();
    const category = document.getElementById("questionCategory").value;

    if (
      !question ||
      !correctAnswer ||
      !wrongAnswer1 ||
      !wrongAnswer2 ||
      !wrongAnswer3
    ) {
      alert("Please fill in all fields.");
      return;
    }

    storage.addCustomQuestion({
      question,
      correctAnswer,
      wrongAnswer1,
      wrongAnswer2,
      wrongAnswer3,
      category,
    });

    loadCustomQuestions();
    e.target.reset();
    alert("Question added!");
  });

  function decodeHtml(html) {
    const txt = document.createElement("textarea");
    txt.innerHTML = html;
    return txt.value;
  }

  function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  }
</script>
</body>
</html>