<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Simple Test</title>
</head>
<body>
    <h1>Simple Test Page</h1>
    <p>If you can see this, HTML is working.</p>
    
    <div id="test-output">Loading JavaScript...</div>
    
    <script>
        console.log("JavaScript is running!");
        document.getElementById('test-output').innerHTML = "✅ JavaScript is working!";
        
        // Test local storage
        try {
            localStorage.setItem('test', 'working');
            const result = localStorage.getItem('test');
            if (result === 'working') {
                document.getElementById('test-output').innerHTML += "<br>✅ Local Storage is working!";
            }
            localStorage.removeItem('test');
        } catch (error) {
            document.getElementById('test-output').innerHTML += "<br>❌ Local Storage error: " + error.message;
        }
    </script>
</body>
</html>
